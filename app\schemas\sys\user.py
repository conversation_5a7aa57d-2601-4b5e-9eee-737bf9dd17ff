from datetime import datetime
from typing import Optional
from pydantic import BaseModel

from app.schemas.base import BaseSchema


class SysUserBase(BaseSchema):
    """系统用户基础Schema"""
    username: str


class SysUserCreate(SysUserBase):
    """创建系统用户"""
    password: str
    active: Optional[int] = 1


class SysUserUpdate(BaseSchema):
    """更新系统用户"""
    username: Optional[str] = None
    password: Optional[str] = None
    active: Optional[int] = None


class SysUserResponse(SysUserBase):
    """系统用户响应"""
    id: int
    token_version: int
    ctime: datetime
    active: int


class SysUserListResponse(BaseSchema):
    """系统用户列表响应"""
    id: int
    username: str
    ctime: datetime
    active: int
