from typing import Optional
from pydantic import BaseModel

from app.schemas.base import BaseSchema


class SysBotBase(BaseSchema):
    """机器人基础Schema"""
    name: str
    api_endpoint: Optional[str] = None
    api_key: Optional[str] = None
    sys_prompt: Optional[str] = None
    notes: Optional[str] = None
    active: Optional[int] = 1


class SysBotCreate(SysBotBase):
    """创建机器人"""
    pass


class SysBotUpdate(BaseSchema):
    """更新机器人"""
    name: Optional[str] = None
    api_endpoint: Optional[str] = None
    api_key: Optional[str] = None
    sys_prompt: Optional[str] = None
    notes: Optional[str] = None
    active: Optional[int] = None


class SysBotResponse(SysBotBase):
    """机器人响应"""
    id: int


class SysBotListResponse(BaseSchema):
    """机器人列表响应"""
    id: int
    name: str
    api_endpoint: Optional[str] = None
    notes: Optional[str] = None
    active: int
