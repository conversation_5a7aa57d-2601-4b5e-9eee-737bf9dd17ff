#!/usr/bin/env python3
"""
测试API结构的脚本
"""

def test_imports():
    """测试所有模块是否能正确导入"""
    try:
        # 测试基础模块
        from app.schemas.base import BaseSchema, PaginationParams, ResponseModel
        print("✓ 基础schemas导入成功")
        
        # 测试系统schemas
        from app.schemas.sys.user import SysUserCreate, SysUserResponse
        from app.schemas.sys.admin import SysAdminCreate, SysAdminResponse
        from app.schemas.sys.bot import SysBotCreate, SysBotResponse
        from app.schemas.sys.tenant import SysTenantCreate, SysTenantResponse
        print("✓ 系统schemas导入成功")
        
        # 测试租户schemas
        from app.schemas.tnt.admin import TntAdminCreate, TntAdminResponse
        from app.schemas.tnt.student import TntStudentCreate, TntStudentResponse
        from app.schemas.tnt.subject import TntSubjectCreate, TntSubjectResponse
        from app.schemas.tnt.framework import TntFrameworkCreate, TntFrameworkResponse
        print("✓ 租户schemas导入成功")
        
        # 测试模型
        from app.models.models import SysUser, SysAdmin, SysBot, SysTenant
        from app.models.models import TntAdmin, TntStudent, TntSubject, TntFramework
        print("✓ 数据模型导入成功")
        
        print("\n🎉 所有模块导入测试通过！")
        return True
        
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        return False
    except Exception as e:
        print(f"❌ 其他错误: {e}")
        return False


def test_api_structure():
    """测试API结构"""
    try:
        # 测试API路由结构
        from app.api.v1.api import api_router
        print("✓ API路由导入成功")
        
        # 打印路由信息
        print("\n📋 API路由结构:")
        for route in api_router.routes:
            if hasattr(route, 'path') and hasattr(route, 'methods'):
                methods = ', '.join(route.methods) if route.methods else 'N/A'
                print(f"  {methods:10} {route.path}")
        
        return True
        
    except ImportError as e:
        print(f"❌ API结构导入错误: {e}")
        return False
    except Exception as e:
        print(f"❌ API结构其他错误: {e}")
        return False


def main():
    """主函数"""
    print("🚀 开始测试TMAI API结构...")
    print("=" * 50)
    
    # 测试导入
    if not test_imports():
        return False
    
    print("\n" + "=" * 50)
    
    # 测试API结构
    if not test_api_structure():
        return False
    
    print("\n" + "=" * 50)
    print("✅ 所有测试通过！API结构正确。")
    
    return True


if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
