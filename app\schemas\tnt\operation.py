from datetime import datetime
from typing import Optional, List
from pydantic import BaseModel

from app.schemas.base import BaseSchema


class ClassProgressResponse(BaseSchema):
    """班级练习进度响应"""
    class_id: int
    class_name: str
    total_exercises: int
    completed_exercises: int
    progress_percentage: float
    students_count: int
    exercises: List[dict] = []  # 练习详情列表


class StudentProgressResponse(BaseSchema):
    """学员练习进度响应"""
    student_id: int
    student_name: str
    total_exercises: int
    completed_exercises: int
    progress_percentage: float
    exercises: List[dict] = []  # 练习完成情况


class ClassStudentStatusResponse(BaseSchema):
    """班级学员情况响应"""
    class_id: int
    class_name: str
    total_students: int
    active_students: int
    students: List[dict] = []  # 学员详情列表


class ExerciseCompletionStatsResponse(BaseSchema):
    """练习完成情况统计响应"""
    exercise_id: int
    exercise_title: str
    total_students: int
    completed_students: int
    completion_rate: float
    avg_duration: Optional[float] = None  # 平均完成时长（分钟）
    completion_details: List[dict] = []  # 完成详情


class StudentExerciseReportResponse(BaseSchema):
    """学员练习报告响应"""
    student_id: int
    student_name: str
    exercise_id: int
    exercise_title: str
    exercise_type: int  # 1：作业单；2：角色扮演
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    duration: Optional[int] = None  # 练习时长（分钟）
    report_url: Optional[str] = None  # 报告文件URL
    status: str  # 状态：未开始、进行中、已完成
    details: Optional[dict] = None  # 详细数据


class OperationDashboardResponse(BaseSchema):
    """运营仪表板响应"""
    total_classes: int
    total_students: int
    total_exercises: int
    active_exercises: int
    recent_completions: List[dict] = []  # 最近完成的练习
    class_progress_summary: List[ClassProgressResponse] = []
