from typing import Optional, List
from pydantic import BaseModel

from app.schemas.base import BaseSchema


class TntSceneBase(BaseSchema):
    """场景基础Schema"""
    pv_scripts: str


class TntSceneCreate(TntSceneBase):
    """创建场景"""
    eid: int
    tenant_id: int


class TntSceneUpdate(BaseSchema):
    """更新场景"""
    pv_scripts: Optional[str] = None


class TntSceneResponse(TntSceneBase):
    """场景响应"""
    id: int
    eid: int
    tenant_id: int


class TntSceneCharacterBase(BaseSchema):
    """场景人物关系基础Schema"""
    played: Optional[int] = 0  # 0：否；1：是（学员扮演）
    priority: Optional[int] = 1


class TntSceneCharacterCreate(TntSceneCharacterBase):
    """创建场景人物关系"""
    sid: int
    cid: int
    tenant_id: int


class TntSceneCharacterUpdate(BaseSchema):
    """更新场景人物关系"""
    played: Optional[int] = None
    priority: Optional[int] = None


class TntSceneCharacterResponse(TntSceneCharacterBase):
    """场景人物关系响应"""
    id: int
    sid: int
    cid: int
    tenant_id: int


class TntSceneGuideBase(BaseSchema):
    """场景指南基础Schema"""
    title: str
    details: str
    priority: Optional[int] = 1


class TntSceneGuideCreate(TntSceneGuideBase):
    """创建场景指南"""
    sid: int
    tenant_id: int


class TntSceneGuideUpdate(BaseSchema):
    """更新场景指南"""
    title: Optional[str] = None
    details: Optional[str] = None
    priority: Optional[int] = None


class TntSceneGuideResponse(TntSceneGuideBase):
    """场景指南响应"""
    id: int
    sid: int
    tenant_id: int


class TntCueBase(BaseSchema):
    """剧本提示基础Schema"""
    content: str
    serial: Optional[int] = 1  # 0：并行发言；1：顺序发言
    priority: Optional[int] = 1
    active: Optional[int] = 1


class TntCueCreate(TntCueBase):
    """创建剧本提示"""
    sid: int
    cid: int
    tenant_id: int


class TntCueUpdate(BaseSchema):
    """更新剧本提示"""
    content: Optional[str] = None
    serial: Optional[int] = None
    priority: Optional[int] = None
    active: Optional[int] = None


class TntCueResponse(TntCueBase):
    """剧本提示响应"""
    id: int
    sid: int
    cid: int
    tenant_id: int


class TntLineBase(BaseSchema):
    """台词基础Schema"""
    pv_topic: str
    pv_ability: str
    pv_restriction: str
    priority: Optional[int] = 1
    active: Optional[int] = 1


class TntLineCreate(TntLineBase):
    """创建台词"""
    cueid: int
    cid: int
    tenant_id: int


class TntLineUpdate(BaseSchema):
    """更新台词"""
    pv_topic: Optional[str] = None
    pv_ability: Optional[str] = None
    pv_restriction: Optional[str] = None
    priority: Optional[int] = None
    active: Optional[int] = None


class TntLineResponse(TntLineBase):
    """台词响应"""
    id: int
    cueid: int
    cid: int
    tenant_id: int


class TntSceneDetailResponse(TntSceneResponse):
    """场景详情响应（包含人物、指南、剧本提示和台词）"""
    characters: List[dict] = []  # 场景人物列表
    guides: List[TntSceneGuideResponse] = []  # 场景指南列表
    cues: List[dict] = []  # 剧本提示列表，包含台词
