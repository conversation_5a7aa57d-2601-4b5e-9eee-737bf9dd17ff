from datetime import datetime, timedelta
from typing import Optional, Union
from jose import JWTError, jwt
from passlib.context import Crypt<PERSON>ontext
from sqlalchemy.orm import Session
from fastapi import HTTPException, status

from app.core.config import settings
from app.models.models import <PERSON>ys<PERSON><PERSON>, SysAdmin, TntAdmin
from app.schemas.base import LoginRequest, LoginResponse

pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")


def verify_password(plain_password: str, hashed_password: str) -> bool:
    """验证密码"""
    return pwd_context.verify(plain_password, hashed_password)


def get_password_hash(password: str) -> str:
    """获取密码哈希"""
    return pwd_context.hash(password)


def create_access_token(data: dict, expires_delta: Optional[timedelta] = None):
    """创建访问令牌"""
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, settings.SECRET_KEY, algorithm=settings.ALGORITHM)
    return encoded_jwt


def verify_token(token: str) -> Optional[dict]:
    """验证令牌"""
    try:
        payload = jwt.decode(token, settings.SECRET_KEY, algorithms=[settings.ALGORITHM])
        return payload
    except JWTError:
        return None


def authenticate_sys_user(db: Session, username: str, password: str) -> Optional[dict]:
    """系统用户认证"""
    user = db.query(SysUser).filter(SysUser.username == username, SysUser.active == 1).first()
    if not user or not verify_password(password, user.passwd):
        return None
    
    # 检查是否是系统管理员
    admin = db.query(SysAdmin).filter(SysAdmin.uid == user.id).first()
    if not admin:
        return None
    
    user_info = {
        "user_id": user.id,
        "username": user.username,
        "admin_id": admin.id,
        "admin_name": admin.name,
        "role": admin.role,
        "user_type": "sys_admin",
        "token_version": user.token_version
    }
    
    return user_info


def authenticate_tenant_user(db: Session, username: str, password: str, tenant_id: Optional[int] = None) -> Optional[dict]:
    """租户用户认证"""
    user = db.query(SysUser).filter(SysUser.username == username, SysUser.active == 1).first()
    if not user or not verify_password(password, user.passwd):
        return None
    
    # 检查是否是租户管理员
    query = db.query(TntAdmin).filter(TntAdmin.uid == user.id)
    if tenant_id:
        query = query.filter(TntAdmin.tenant_id == tenant_id)
    
    admin = query.first()
    if not admin:
        return None
    
    user_info = {
        "user_id": user.id,
        "username": user.username,
        "admin_id": admin.id,
        "admin_name": admin.name,
        "role": admin.role,
        "tenant_id": admin.tenant_id,
        "user_type": "tenant_admin",
        "token_version": user.token_version
    }
    
    return user_info


def login_sys_user(db: Session, login_data: LoginRequest) -> LoginResponse:
    """系统用户登录"""
    user_info = authenticate_sys_user(db, login_data.username, login_data.password)
    if not user_info:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="用户名或密码错误"
        )
    
    access_token = create_access_token(data={"sub": str(user_info["user_id"]), **user_info})
    
    return LoginResponse(
        access_token=access_token,
        user_info=user_info
    )


def login_tenant_user(db: Session, login_data: LoginRequest, tenant_id: Optional[int] = None) -> LoginResponse:
    """租户用户登录"""
    user_info = authenticate_tenant_user(db, login_data.username, login_data.password, tenant_id)
    if not user_info:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="用户名或密码错误"
        )
    
    access_token = create_access_token(data={"sub": str(user_info["user_id"]), **user_info})
    
    return LoginResponse(
        access_token=access_token,
        user_info=user_info
    )


def change_password(db: Session, user_id: int, old_password: str, new_password: str) -> bool:
    """修改密码"""
    user = db.query(SysUser).filter(SysUser.id == user_id).first()
    if not user or not verify_password(old_password, user.passwd):
        return False
    
    user.passwd = get_password_hash(new_password)
    user.token_version += 1  # 使所有现有token失效
    db.commit()
    return True


def logout_user(db: Session, user_id: int) -> bool:
    """用户登出（使token失效）"""
    user = db.query(SysUser).filter(SysUser.id == user_id).first()
    if not user:
        return False
    
    user.token_version += 1
    db.commit()
    return True
