from datetime import datetime
from typing import Optional
from pydantic import BaseModel, ConfigDict


class BaseSchema(BaseModel):
    """基础Schema类"""
    model_config = ConfigDict(from_attributes=True)


class PaginationParams(BaseModel):
    """分页参数"""
    page: int = 1
    page_size: int = 20


class PaginationResponse(BaseModel):
    """分页响应"""
    total: int
    page: int
    page_size: int
    pages: int


class ResponseModel(BaseModel):
    """统一响应模型"""
    code: int = 200
    message: str = "success"
    data: Optional[dict] = None


class LoginRequest(BaseModel):
    """登录请求"""
    username: str
    password: str


class LoginResponse(BaseModel):
    """登录响应"""
    access_token: str
    token_type: str = "bearer"
    user_info: dict


class PasswordChangeRequest(BaseModel):
    """修改密码请求"""
    old_password: str
    new_password: str


class UserProfileUpdate(BaseModel):
    """用户资料更新"""
    name: Optional[str] = None
    gender: Optional[int] = None
    notes: Optional[str] = None
