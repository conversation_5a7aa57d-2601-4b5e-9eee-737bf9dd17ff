from typing import Optional
from pydantic import BaseModel

from app.schemas.base import BaseSchema


class SysBconfBase(BaseSchema):
    """系统机器人配置基础Schema"""
    bid: int
    notes: Optional[str] = None


class SysBconfCreate(SysBconfBase):
    """创建系统机器人配置"""
    key: str


class SysBconfUpdate(BaseSchema):
    """更新系统机器人配置"""
    bid: Optional[int] = None
    notes: Optional[str] = None


class SysBconfResponse(SysBconfBase):
    """系统机器人配置响应"""
    key: str
    bot_name: Optional[str] = None  # 关联机器人名称


class SysBconfListResponse(BaseSchema):
    """系统机器人配置列表响应"""
    key: str
    bid: int
    notes: Optional[str] = None
    bot_name: Optional[str] = None
