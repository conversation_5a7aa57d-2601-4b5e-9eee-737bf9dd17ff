from typing import List, Optional
from sqlalchemy.orm import Session, joinedload
from fastapi import HTTPException, status

from app.models.models import TntBconf, SysBot, SysTenant
from app.schemas.tnt.bconf import TntBconfCreate, TntBconfUpdate, TntBconfResponse, TntBconfListResponse
from app.schemas.base import PaginationParams


def create_bconf(db: Session, bconf_data: TntBconfCreate) -> TntBconfResponse:
    """创建租户机器人配置"""
    # 检查机器人是否存在
    bot = db.query(SysBot).filter(SysBot.id == bconf_data.bid, SysBot.active == 1).first()
    if not bot:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="机器人不存在或已被禁用"
        )
    
    # 检查租户是否存在
    tenant = db.query(SysTenant).filter(SysTenant.id == bconf_data.tenant_id, SysTenant.active == 1).first()
    if not tenant:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="租户不存在或已被禁用"
        )
    
    # 检查配置key在该租户下是否已存在
    existing_bconf = db.query(TntBconf).filter(
        TntBconf.key == bconf_data.key,
        TntBconf.tenant_id == bconf_data.tenant_id
    ).first()
    if existing_bconf:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="配置key在该租户下已存在"
        )
    
    # 创建配置
    db_bconf = TntBconf(**bconf_data.model_dump())
    
    db.add(db_bconf)
    db.commit()
    db.refresh(db_bconf)
    
    # 返回包含机器人名称的响应
    response = TntBconfResponse.model_validate(db_bconf)
    response.bot_name = bot.name
    return response


def get_bconf(db: Session, key: str, tenant_id: int) -> Optional[TntBconfResponse]:
    """获取租户机器人配置"""
    bconf = db.query(TntBconf).options(joinedload(TntBconf.bot)).filter(
        TntBconf.key == key,
        TntBconf.tenant_id == tenant_id
    ).first()
    if not bconf:
        return None
    
    response = TntBconfResponse.model_validate(bconf)
    response.bot_name = bconf.bot.name if bconf.bot else None
    return response


def get_bconfs(db: Session, pagination: PaginationParams, tenant_id: int, 
               bot_id: Optional[int] = None) -> tuple[List[TntBconfListResponse], int]:
    """获取租户机器人配置列表"""
    query = db.query(TntBconf).options(joinedload(TntBconf.bot)).filter(
        TntBconf.tenant_id == tenant_id
    )
    
    # 机器人过滤
    if bot_id:
        query = query.filter(TntBconf.bid == bot_id)
    
    # 获取总数
    total = query.count()
    
    # 分页
    offset = (pagination.page - 1) * pagination.page_size
    bconfs = query.offset(offset).limit(pagination.page_size).all()
    
    result = []
    for bconf in bconfs:
        response = TntBconfListResponse.model_validate(bconf)
        response.bot_name = bconf.bot.name if bconf.bot else None
        result.append(response)
    
    return result, total


def update_bconf(db: Session, key: str, tenant_id: int, bconf_data: TntBconfUpdate) -> Optional[TntBconfResponse]:
    """更新租户机器人配置"""
    bconf = db.query(TntBconf).options(joinedload(TntBconf.bot)).filter(
        TntBconf.key == key,
        TntBconf.tenant_id == tenant_id
    ).first()
    if not bconf:
        return None
    
    # 检查新的机器人是否存在
    if bconf_data.bid:
        bot = db.query(SysBot).filter(SysBot.id == bconf_data.bid, SysBot.active == 1).first()
        if not bot:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="机器人不存在或已被禁用"
            )
    
    # 更新字段
    update_data = bconf_data.model_dump(exclude_unset=True)
    for field, value in update_data.items():
        setattr(bconf, field, value)
    
    db.commit()
    db.refresh(bconf)
    
    response = TntBconfResponse.model_validate(bconf)
    response.bot_name = bconf.bot.name if bconf.bot else None
    return response


def delete_bconf(db: Session, key: str, tenant_id: int) -> bool:
    """删除租户机器人配置"""
    bconf = db.query(TntBconf).filter(
        TntBconf.key == key,
        TntBconf.tenant_id == tenant_id
    ).first()
    if not bconf:
        return False
    
    db.delete(bconf)
    db.commit()
    return True


def get_bconfs_by_bot(db: Session, bot_id: int, tenant_id: int) -> List[TntBconfListResponse]:
    """获取指定机器人在指定租户下的所有配置"""
    bconfs = db.query(TntBconf).options(joinedload(TntBconf.bot)).filter(
        TntBconf.bid == bot_id,
        TntBconf.tenant_id == tenant_id
    ).all()
    
    result = []
    for bconf in bconfs:
        response = TntBconfListResponse.model_validate(bconf)
        response.bot_name = bconf.bot.name if bconf.bot else None
        result.append(response)
    
    return result
