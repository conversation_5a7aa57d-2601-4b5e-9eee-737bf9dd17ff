from typing import List, Optional
from sqlalchemy.orm import Session, joinedload
from fastapi import HTTPException, status

from app.models.models import SysAdmin, SysUser
from app.schemas.sys.admin import SysAdminCreate, SysAdminUpdate, SysAdminResponse, SysAdminListResponse
from app.schemas.base import PaginationParams


def create_admin(db: Session, admin_data: SysAdminCreate) -> SysAdminResponse:
    """创建系统管理员"""
    # 检查用户是否存在且有效
    user = db.query(SysUser).filter(SysUser.id == admin_data.uid, SysUser.active == 1).first()
    if not user:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="用户不存在或已被禁用"
        )
    
    # 检查用户是否已经是管理员
    existing_admin = db.query(SysAdmin).filter(SysAdmin.uid == admin_data.uid).first()
    if existing_admin:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="该用户已经是系统管理员"
        )
    
    # 创建管理员
    db_admin = SysAdmin(
        uid=admin_data.uid,
        name=admin_data.name,
        role=admin_data.role
    )
    
    db.add(db_admin)
    db.commit()
    db.refresh(db_admin)
    
    # 返回包含用户名的响应
    response = SysAdminResponse.model_validate(db_admin)
    response.username = user.username
    return response


def get_admin(db: Session, admin_id: int) -> Optional[SysAdminResponse]:
    """获取系统管理员"""
    admin = db.query(SysAdmin).options(joinedload(SysAdmin.user)).filter(SysAdmin.id == admin_id).first()
    if not admin:
        return None
    
    response = SysAdminResponse.model_validate(admin)
    response.username = admin.user.username if admin.user else None
    return response


def get_admins(db: Session, pagination: PaginationParams, search: Optional[str] = None) -> tuple[List[SysAdminListResponse], int]:
    """获取系统管理员列表"""
    query = db.query(SysAdmin).options(joinedload(SysAdmin.user))
    
    # 搜索过滤
    if search:
        query = query.join(SysUser).filter(
            (SysAdmin.name.contains(search)) | 
            (SysUser.username.contains(search))
        )
    
    # 获取总数
    total = query.count()
    
    # 分页
    offset = (pagination.page - 1) * pagination.page_size
    admins = query.offset(offset).limit(pagination.page_size).all()
    
    result = []
    for admin in admins:
        response = SysAdminListResponse.model_validate(admin)
        response.username = admin.user.username if admin.user else None
        result.append(response)
    
    return result, total


def update_admin(db: Session, admin_id: int, admin_data: SysAdminUpdate) -> Optional[SysAdminResponse]:
    """更新系统管理员"""
    admin = db.query(SysAdmin).options(joinedload(SysAdmin.user)).filter(SysAdmin.id == admin_id).first()
    if not admin:
        return None
    
    # 更新字段
    update_data = admin_data.model_dump(exclude_unset=True)
    for field, value in update_data.items():
        setattr(admin, field, value)
    
    db.commit()
    db.refresh(admin)
    
    response = SysAdminResponse.model_validate(admin)
    response.username = admin.user.username if admin.user else None
    return response


def delete_admin(db: Session, admin_id: int) -> bool:
    """删除系统管理员"""
    admin = db.query(SysAdmin).filter(SysAdmin.id == admin_id).first()
    if not admin:
        return False
    
    db.delete(admin)
    db.commit()
    return True


def get_admin_by_user_id(db: Session, user_id: int) -> Optional[SysAdmin]:
    """根据用户ID获取管理员信息"""
    return db.query(SysAdmin).filter(SysAdmin.uid == user_id).first()
