from typing import List, Optional
from sqlalchemy.orm import Session
from sqlalchemy import func
from fastapi import HTTPException, status

from app.models.models import SysUser
from app.schemas.sys.user import SysUserCreate, SysUserUpdate, SysUserResponse, SysUserListResponse
from app.schemas.base import PaginationParams
from app.services.auth import get_password_hash


def create_user(db: Session, user_data: SysUserCreate) -> SysUserResponse:
    """创建系统用户"""
    # 检查用户名是否已存在
    existing_user = db.query(SysUser).filter(SysUser.username == user_data.username).first()
    if existing_user:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="用户名已存在"
        )
    
    # 创建新用户
    hashed_password = get_password_hash(user_data.password)
    db_user = SysUser(
        username=user_data.username,
        passwd=hashed_password,
        active=user_data.active
    )
    
    db.add(db_user)
    db.commit()
    db.refresh(db_user)
    
    return SysUserResponse.model_validate(db_user)


def get_user(db: Session, user_id: int) -> Optional[SysUserResponse]:
    """获取系统用户"""
    user = db.query(SysUser).filter(SysUser.id == user_id).first()
    if not user:
        return None
    return SysUserResponse.model_validate(user)


def get_users(db: Session, pagination: PaginationParams, search: Optional[str] = None) -> tuple[List[SysUserListResponse], int]:
    """获取系统用户列表"""
    query = db.query(SysUser)
    
    # 搜索过滤
    if search:
        query = query.filter(SysUser.username.contains(search))
    
    # 获取总数
    total = query.count()
    
    # 分页
    offset = (pagination.page - 1) * pagination.page_size
    users = query.offset(offset).limit(pagination.page_size).all()
    
    return [SysUserListResponse.model_validate(user) for user in users], total


def update_user(db: Session, user_id: int, user_data: SysUserUpdate) -> Optional[SysUserResponse]:
    """更新系统用户"""
    user = db.query(SysUser).filter(SysUser.id == user_id).first()
    if not user:
        return None
    
    # 检查用户名是否已被其他用户使用
    if user_data.username and user_data.username != user.username:
        existing_user = db.query(SysUser).filter(
            SysUser.username == user_data.username,
            SysUser.id != user_id
        ).first()
        if existing_user:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="用户名已存在"
            )
    
    # 更新字段
    update_data = user_data.model_dump(exclude_unset=True)
    if "password" in update_data:
        update_data["passwd"] = get_password_hash(update_data.pop("password"))
        user.token_version += 1  # 密码修改后使所有token失效
    
    for field, value in update_data.items():
        setattr(user, field, value)
    
    db.commit()
    db.refresh(user)
    
    return SysUserResponse.model_validate(user)


def delete_user(db: Session, user_id: int) -> bool:
    """删除系统用户（软删除）"""
    user = db.query(SysUser).filter(SysUser.id == user_id).first()
    if not user:
        return False
    
    user.active = 0
    db.commit()
    return True


def get_user_by_username(db: Session, username: str) -> Optional[SysUser]:
    """根据用户名获取用户"""
    return db.query(SysUser).filter(SysUser.username == username, SysUser.active == 1).first()
