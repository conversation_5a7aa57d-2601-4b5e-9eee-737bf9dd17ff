from typing import Optional
from pydantic import BaseModel

from app.schemas.base import BaseSchema


class SysAdminBase(BaseSchema):
    """系统管理员基础Schema"""
    name: str
    role: Optional[int] = 1  # 0：超级管理员；1：管理员


class SysAdminCreate(SysAdminBase):
    """创建系统管理员"""
    uid: int


class SysAdminUpdate(BaseSchema):
    """更新系统管理员"""
    name: Optional[str] = None
    role: Optional[int] = None


class SysAdminResponse(SysAdminBase):
    """系统管理员响应"""
    id: int
    uid: int
    username: Optional[str] = None  # 关联用户名


class SysAdminListResponse(BaseSchema):
    """系统管理员列表响应"""
    id: int
    uid: int
    name: str
    role: int
    username: Optional[str] = None
