from typing import List, Optional
from sqlalchemy.orm import Session, joinedload
from fastapi import HTTPException, status

from app.models.models import TntStudent, SysUser, SysTenant
from app.schemas.tnt.student import TntStudentCreate, TntStudentUpdate, TntStudentResponse, TntStudentListResponse
from app.schemas.base import PaginationParams


def create_student(db: Session, student_data: TntStudentCreate) -> TntStudentResponse:
    """创建学员"""
    # 检查用户是否存在且有效
    user = db.query(SysUser).filter(SysUser.id == student_data.uid, SysUser.active == 1).first()
    if not user:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="用户不存在或已被禁用"
        )
    
    # 检查租户是否存在且有效
    tenant = db.query(SysTenant).filter(SysTenant.id == student_data.tenant_id, SysTenant.active == 1).first()
    if not tenant:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="租户不存在或已被禁用"
        )
    
    # 检查用户在该租户下是否已经是学员
    existing_student = db.query(TntStudent).filter(
        TntStudent.uid == student_data.uid,
        TntStudent.tenant_id == student_data.tenant_id
    ).first()
    if existing_student:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="该用户在此租户下已经是学员"
        )
    
    # 创建学员
    db_student = TntStudent(**student_data.model_dump())
    
    db.add(db_student)
    db.commit()
    db.refresh(db_student)
    
    # 返回包含用户名的响应
    response = TntStudentResponse.model_validate(db_student)
    response.username = user.username
    return response


def get_student(db: Session, student_id: int, tenant_id: int) -> Optional[TntStudentResponse]:
    """获取学员"""
    student = db.query(TntStudent).options(joinedload(TntStudent.user)).filter(
        TntStudent.id == student_id,
        TntStudent.tenant_id == tenant_id
    ).first()
    if not student:
        return None
    
    response = TntStudentResponse.model_validate(student)
    response.username = student.user.username if student.user else None
    return response


def get_students(db: Session, pagination: PaginationParams, tenant_id: int, 
                 search: Optional[str] = None) -> tuple[List[TntStudentListResponse], int]:
    """获取学员列表"""
    query = db.query(TntStudent).options(joinedload(TntStudent.user)).filter(
        TntStudent.tenant_id == tenant_id
    )
    
    # 搜索过滤
    if search:
        query = query.join(SysUser).filter(
            (TntStudent.name.contains(search)) | 
            (SysUser.username.contains(search))
        )
    
    # 获取总数
    total = query.count()
    
    # 分页
    offset = (pagination.page - 1) * pagination.page_size
    students = query.offset(offset).limit(pagination.page_size).all()
    
    result = []
    for student in students:
        response = TntStudentListResponse.model_validate(student)
        response.username = student.user.username if student.user else None
        result.append(response)
    
    return result, total


def update_student(db: Session, student_id: int, tenant_id: int, student_data: TntStudentUpdate) -> Optional[TntStudentResponse]:
    """更新学员"""
    student = db.query(TntStudent).options(joinedload(TntStudent.user)).filter(
        TntStudent.id == student_id,
        TntStudent.tenant_id == tenant_id
    ).first()
    if not student:
        return None
    
    # 更新字段
    update_data = student_data.model_dump(exclude_unset=True)
    for field, value in update_data.items():
        setattr(student, field, value)
    
    db.commit()
    db.refresh(student)
    
    response = TntStudentResponse.model_validate(student)
    response.username = student.user.username if student.user else None
    return response


def delete_student(db: Session, student_id: int, tenant_id: int) -> bool:
    """删除学员"""
    student = db.query(TntStudent).filter(
        TntStudent.id == student_id,
        TntStudent.tenant_id == tenant_id
    ).first()
    if not student:
        return False
    
    db.delete(student)
    db.commit()
    return True


def get_students_by_class(db: Session, class_id: int, tenant_id: int) -> List[TntStudentListResponse]:
    """获取班级下的学员列表"""
    from app.models.models import TntClassStudent
    
    students = db.query(TntStudent).options(joinedload(TntStudent.user)).join(
        TntClassStudent, TntStudent.id == TntClassStudent.sid
    ).filter(
        TntClassStudent.cid == class_id,
        TntClassStudent.tenant_id == tenant_id,
        TntClassStudent.active == 1
    ).all()
    
    result = []
    for student in students:
        response = TntStudentListResponse.model_validate(student)
        response.username = student.user.username if student.user else None
        result.append(response)
    
    return result


def get_available_students(db: Session, tenant_id: int, exclude_class_id: Optional[int] = None) -> List[TntStudentListResponse]:
    """获取可用的学员列表（未分配到指定班级的学员）"""
    query = db.query(TntStudent).options(joinedload(TntStudent.user)).filter(
        TntStudent.tenant_id == tenant_id
    )
    
    if exclude_class_id:
        from app.models.models import TntClassStudent
        # 排除已在指定班级的学员
        subquery = db.query(TntClassStudent.sid).filter(
            TntClassStudent.cid == exclude_class_id,
            TntClassStudent.active == 1
        ).subquery()
        query = query.filter(~TntStudent.id.in_(subquery))
    
    students = query.all()
    
    result = []
    for student in students:
        response = TntStudentListResponse.model_validate(student)
        response.username = student.user.username if student.user else None
        result.append(response)
    
    return result
