from typing import Optional, List
from pydantic import BaseModel

from app.schemas.base import BaseSchema


class TntFrameworkBase(BaseSchema):
    """理论框架基础Schema"""
    name: str
    description: Optional[str] = None
    logo: Optional[str] = None
    priority: Optional[int] = 1
    active: Optional[int] = 1


class TntFrameworkCreate(TntFrameworkBase):
    """创建理论框架"""
    tenant_id: int


class TntFrameworkUpdate(BaseSchema):
    """更新理论框架"""
    name: Optional[str] = None
    description: Optional[str] = None
    logo: Optional[str] = None
    priority: Optional[int] = None
    active: Optional[int] = None


class TntFrameworkResponse(TntFrameworkBase):
    """理论框架响应"""
    id: int
    tenant_id: int


class TntFrameworkListResponse(BaseSchema):
    """理论框架列表响应"""
    id: int
    tenant_id: int
    name: str
    description: Optional[str] = None
    logo: Optional[str] = None
    priority: int
    active: int


class TntModuleBase(BaseSchema):
    """理论模块基础Schema"""
    name: str
    description: Optional[str] = None
    priority: Optional[int] = 1
    active: Optional[int] = 1


class TntModuleCreate(TntModuleBase):
    """创建理论模块"""
    fid: int
    tenant_id: int


class TntModuleUpdate(BaseSchema):
    """更新理论模块"""
    name: Optional[str] = None
    description: Optional[str] = None
    priority: Optional[int] = None
    active: Optional[int] = None


class TntModuleResponse(TntModuleBase):
    """理论模块响应"""
    id: int
    fid: int
    tenant_id: int


class TntModuleListResponse(BaseSchema):
    """理论模块列表响应"""
    id: int
    fid: int
    tenant_id: int
    name: str
    description: Optional[str] = None
    priority: int
    active: int


class TntFrameworkDetailResponse(TntFrameworkResponse):
    """理论框架详情响应（包含模块列表）"""
    modules: List[TntModuleListResponse] = []
