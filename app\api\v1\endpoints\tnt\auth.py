from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session

from app.db.session import get_db
from app.schemas.base import LoginRequest, LoginResponse, PasswordChangeRequest, ResponseModel
from app.services.auth import login_tenant_user, change_password, logout_user
from app.core.deps import get_tenant_admin_user

router = APIRouter()


@router.post("/login", response_model=LoginResponse, summary="租户管理员登录")
def tenant_admin_login(
    login_data: LoginRequest,
    db: Session = Depends(get_db)
):
    """
    租户管理员登录
    
    - **username**: 用户名
    - **password**: 密码
    """
    return login_tenant_user(db, login_data)


@router.post("/logout", response_model=ResponseModel, summary="租户管理员登出")
def tenant_admin_logout(
    current_user: dict = Depends(get_tenant_admin_user),
    db: Session = Depends(get_db)
):
    """
    租户管理员登出
    """
    success = logout_user(db, current_user["user_id"])
    if not success:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="登出失败"
        )
    
    return ResponseModel(message="登出成功")


@router.post("/change-password", response_model=ResponseModel, summary="修改密码")
def change_tenant_admin_password(
    password_data: PasswordChangeRequest,
    current_user: dict = Depends(get_tenant_admin_user),
    db: Session = Depends(get_db)
):
    """
    修改租户管理员密码
    
    - **old_password**: 旧密码
    - **new_password**: 新密码
    """
    success = change_password(
        db, 
        current_user["user_id"], 
        password_data.old_password, 
        password_data.new_password
    )
    
    if not success:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="旧密码错误"
        )
    
    return ResponseModel(message="密码修改成功")


@router.get("/profile", response_model=dict, summary="获取个人信息")
def get_tenant_admin_profile(
    current_user: dict = Depends(get_tenant_admin_user)
):
    """
    获取租户管理员个人信息
    """
    role_names = {1: "租户管理员", 2: "教学运营人员"}
    
    return {
        "user_id": current_user["user_id"],
        "username": current_user["username"],
        "admin_id": current_user["admin_id"],
        "admin_name": current_user["admin_name"],
        "role": current_user["role"],
        "role_name": role_names.get(current_user["role"], "未知"),
        "tenant_id": current_user["tenant_id"]
    }
