from datetime import datetime
from typing import Optional, List
from pydantic import BaseModel

from app.schemas.base import BaseSchema


class TntQuestionBase(BaseSchema):
    """问题基础Schema"""
    title: str
    bgtext: Optional[str] = None
    bgvideo: Optional[str] = None
    notes: Optional[str] = None
    pv_skills: Optional[str] = None
    pv_rules: Optional[str] = None
    pv_formats: Optional[str] = None
    active: Optional[int] = 1


class TntQuestionCreate(TntQuestionBase):
    """创建问题"""
    tenant_id: int


class TntQuestionUpdate(BaseSchema):
    """更新问题"""
    title: Optional[str] = None
    bgtext: Optional[str] = None
    bgvideo: Optional[str] = None
    notes: Optional[str] = None
    pv_skills: Optional[str] = None
    pv_rules: Optional[str] = None
    pv_formats: Optional[str] = None
    active: Optional[int] = None


class TntQuestionResponse(TntQuestionBase):
    """问题响应"""
    id: int
    tenant_id: int
    ctime: datetime


class TntQuestionListResponse(BaseSchema):
    """问题列表响应"""
    id: int
    tenant_id: int
    title: str
    bgtext: Optional[str] = None
    notes: Optional[str] = None
    ctime: datetime
    active: int


class TntQuestionGuideBase(BaseSchema):
    """问题指南基础Schema"""
    title: str
    details: str
    priority: Optional[int] = 1


class TntQuestionGuideCreate(TntQuestionGuideBase):
    """创建问题指南"""
    qid: int
    tenant_id: int


class TntQuestionGuideUpdate(BaseSchema):
    """更新问题指南"""
    title: Optional[str] = None
    details: Optional[str] = None
    priority: Optional[int] = None


class TntQuestionGuideResponse(TntQuestionGuideBase):
    """问题指南响应"""
    id: int
    qid: int
    tenant_id: int


class TntQuestionModuleAssociation(BaseSchema):
    """问题-理论模块关联"""
    module_ids: List[int]


class TntQuestionSubjectAssociation(BaseSchema):
    """问题-主题关联"""
    subject_ids: List[int]


class TntQuestionDetailResponse(TntQuestionResponse):
    """问题详情响应（包含指南、关联模块、关联主题）"""
    guides: List[TntQuestionGuideResponse] = []
    modules: List[dict] = []  # 关联的理论模块
    subjects: List[dict] = []  # 关联的主题
