from fastapi import APIRouter

from app.api.v1.endpoints import system
from app.api.v1.endpoints.sys import auth as sys_auth, users as sys_users, admins as sys_admins, bots as sys_bots, bconfs as sys_bconfs, tenants as sys_tenants
from app.api.v1.endpoints.tnt import auth as tnt_auth

api_router = APIRouter()

# System endpoints
api_router.include_router(system.router, prefix="/sys", tags=["system"])

# System management endpoints
api_router.include_router(sys_auth.router, prefix="/sys/auth", tags=["sys-auth"])
api_router.include_router(sys_users.router, prefix="/sys/users", tags=["sys-users"])
api_router.include_router(sys_admins.router, prefix="/sys/admins", tags=["sys-admins"])
api_router.include_router(sys_bots.router, prefix="/sys/bots", tags=["sys-bots"])
api_router.include_router(sys_bconfs.router, prefix="/sys/bconfs", tags=["sys-bconfs"])
api_router.include_router(sys_tenants.router, prefix="/sys/tenants", tags=["sys-tenants"])

# Tenant management endpoints
api_router.include_router(tnt_auth.router, prefix="/tnt/auth", tags=["tnt-auth"])
