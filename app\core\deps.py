from typing import Optional
from fastapi import Depends, HTTPException, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from sqlalchemy.orm import Session

from app.db.session import get_db
from app.services.auth import verify_token
from app.models.models import SysUser

security = HTTPBearer()


def get_current_user(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    db: Session = Depends(get_db)
) -> dict:
    """获取当前用户信息"""
    token = credentials.credentials
    payload = verify_token(token)
    
    if not payload:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="无效的token"
        )
    
    user_id = payload.get("sub")
    if not user_id:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="无效的token"
        )
    
    # 检查用户是否存在且有效
    user = db.query(SysUser).filter(SysUser.id == int(user_id), SysUser.active == 1).first()
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="用户不存在或已被禁用"
        )
    
    # 检查token版本
    token_version = payload.get("token_version", 0)
    if token_version != user.token_version:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="token已失效，请重新登录"
        )
    
    return payload


def get_sys_admin_user(current_user: dict = Depends(get_current_user)) -> dict:
    """获取系统管理员用户"""
    if current_user.get("user_type") != "sys_admin":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="需要系统管理员权限"
        )
    return current_user


def get_super_admin_user(current_user: dict = Depends(get_sys_admin_user)) -> dict:
    """获取超级管理员用户"""
    if current_user.get("role") != 0:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="需要超级管理员权限"
        )
    return current_user


def get_tenant_admin_user(current_user: dict = Depends(get_current_user)) -> dict:
    """获取租户管理员用户"""
    if current_user.get("user_type") != "tenant_admin":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="需要租户管理员权限"
        )
    return current_user


def get_tenant_manager_user(current_user: dict = Depends(get_tenant_admin_user)) -> dict:
    """获取租户管理员用户（管理员角色）"""
    if current_user.get("role") != 1:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="需要租户管理员权限"
        )
    return current_user


def get_operation_user(current_user: dict = Depends(get_tenant_admin_user)) -> dict:
    """获取教学运营人员用户"""
    if current_user.get("role") != 2:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="需要教学运营人员权限"
        )
    return current_user


def get_sys_or_tenant_admin(current_user: dict = Depends(get_current_user)) -> dict:
    """获取系统管理员或租户管理员用户"""
    user_type = current_user.get("user_type")
    if user_type not in ["sys_admin", "tenant_admin"]:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="需要管理员权限"
        )
    return current_user


def get_tenant_id_from_user(current_user: dict) -> Optional[int]:
    """从用户信息中获取租户ID"""
    return current_user.get("tenant_id")
