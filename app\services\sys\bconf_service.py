from typing import List, Optional
from sqlalchemy.orm import Session, joinedload
from fastapi import HTTPException, status

from app.models.models import SysBconf, SysBot
from app.schemas.sys.bconf import SysBconfCreate, SysBconfUpdate, SysBconfResponse, SysBconfListResponse
from app.schemas.base import PaginationParams


def create_bconf(db: Session, bconf_data: SysBconfCreate) -> SysBconfResponse:
    """创建系统机器人配置"""
    # 检查机器人是否存在
    bot = db.query(SysBot).filter(SysBot.id == bconf_data.bid, SysBot.active == 1).first()
    if not bot:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="机器人不存在或已被禁用"
        )
    
    # 检查配置key是否已存在
    existing_bconf = db.query(SysBconf).filter(SysBconf.key == bconf_data.key).first()
    if existing_bconf:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="配置key已存在"
        )
    
    # 创建配置
    db_bconf = SysBconf(**bconf_data.model_dump())
    
    db.add(db_bconf)
    db.commit()
    db.refresh(db_bconf)
    
    # 返回包含机器人名称的响应
    response = SysBconfResponse.model_validate(db_bconf)
    response.bot_name = bot.name
    return response


def get_bconf(db: Session, key: str) -> Optional[SysBconfResponse]:
    """获取系统机器人配置"""
    bconf = db.query(SysBconf).options(joinedload(SysBconf.bot)).filter(SysBconf.key == key).first()
    if not bconf:
        return None
    
    response = SysBconfResponse.model_validate(bconf)
    response.bot_name = bconf.bot.name if bconf.bot else None
    return response


def get_bconfs(db: Session, pagination: PaginationParams, bot_id: Optional[int] = None) -> tuple[List[SysBconfListResponse], int]:
    """获取系统机器人配置列表"""
    query = db.query(SysBconf).options(joinedload(SysBconf.bot))
    
    # 机器人过滤
    if bot_id:
        query = query.filter(SysBconf.bid == bot_id)
    
    # 获取总数
    total = query.count()
    
    # 分页
    offset = (pagination.page - 1) * pagination.page_size
    bconfs = query.offset(offset).limit(pagination.page_size).all()
    
    result = []
    for bconf in bconfs:
        response = SysBconfListResponse.model_validate(bconf)
        response.bot_name = bconf.bot.name if bconf.bot else None
        result.append(response)
    
    return result, total


def update_bconf(db: Session, key: str, bconf_data: SysBconfUpdate) -> Optional[SysBconfResponse]:
    """更新系统机器人配置"""
    bconf = db.query(SysBconf).options(joinedload(SysBconf.bot)).filter(SysBconf.key == key).first()
    if not bconf:
        return None
    
    # 检查新的机器人是否存在
    if bconf_data.bid:
        bot = db.query(SysBot).filter(SysBot.id == bconf_data.bid, SysBot.active == 1).first()
        if not bot:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="机器人不存在或已被禁用"
            )
    
    # 更新字段
    update_data = bconf_data.model_dump(exclude_unset=True)
    for field, value in update_data.items():
        setattr(bconf, field, value)
    
    db.commit()
    db.refresh(bconf)
    
    response = SysBconfResponse.model_validate(bconf)
    response.bot_name = bconf.bot.name if bconf.bot else None
    return response


def delete_bconf(db: Session, key: str) -> bool:
    """删除系统机器人配置"""
    bconf = db.query(SysBconf).filter(SysBconf.key == key).first()
    if not bconf:
        return False
    
    db.delete(bconf)
    db.commit()
    return True


def get_bconfs_by_bot(db: Session, bot_id: int) -> List[SysBconfListResponse]:
    """获取指定机器人的所有配置"""
    bconfs = db.query(SysBconf).options(joinedload(SysBconf.bot)).filter(SysBconf.bid == bot_id).all()
    
    result = []
    for bconf in bconfs:
        response = SysBconfListResponse.model_validate(bconf)
        response.bot_name = bconf.bot.name if bconf.bot else None
        result.append(response)
    
    return result
