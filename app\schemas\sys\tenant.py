from datetime import datetime
from typing import Optional
from pydantic import BaseModel

from app.schemas.base import BaseSchema


class SysTenantBase(BaseSchema):
    """租户基础Schema"""
    code: str
    name: Optional[str] = None
    notes: Optional[str] = None
    active: Optional[int] = 1


class SysTenantCreate(SysTenantBase):
    """创建租户"""
    pass


class SysTenantUpdate(BaseSchema):
    """更新租户"""
    code: Optional[str] = None
    name: Optional[str] = None
    notes: Optional[str] = None
    active: Optional[int] = None


class SysTenantResponse(SysTenantBase):
    """租户响应"""
    id: int
    ctime: datetime


class SysTenantListResponse(BaseSchema):
    """租户列表响应"""
    id: int
    code: str
    name: Optional[str] = None
    notes: Optional[str] = None
    ctime: datetime
    active: int
