from datetime import datetime
from typing import Optional
from pydantic import BaseModel

from app.schemas.base import BaseSchema


class TntCharacterBase(BaseSchema):
    """人物基础Schema"""
    name: str
    gender: Optional[int] = 0  # 0：未知；1：男；2：女
    avatar: str
    profile: str
    timbre_type: Optional[int] = 0  # 0：未设置；1：火山引擎
    timbre: Optional[str] = None
    notes: Optional[str] = None
    pv_profile: Optional[str] = None
    pv_ability: Optional[str] = None
    pv_restriction: Optional[str] = None
    published: Optional[int] = 0  # 0：未发布；1：已发布
    active: Optional[int] = 1


class TntCharacterCreate(TntCharacterBase):
    """创建人物"""
    tenant_id: int


class TntCharacterUpdate(BaseSchema):
    """更新人物"""
    name: Optional[str] = None
    gender: Optional[int] = None
    avatar: Optional[str] = None
    profile: Optional[str] = None
    timbre_type: Optional[int] = None
    timbre: Optional[str] = None
    notes: Optional[str] = None
    pv_profile: Optional[str] = None
    pv_ability: Optional[str] = None
    pv_restriction: Optional[str] = None
    published: Optional[int] = None
    active: Optional[int] = None


class TntCharacterResponse(TntCharacterBase):
    """人物响应"""
    id: int
    tenant_id: int
    ctime: datetime


class TntCharacterListResponse(BaseSchema):
    """人物列表响应"""
    id: int
    tenant_id: int
    name: str
    gender: int
    avatar: str
    profile: str
    published: int
    ctime: datetime
    active: int
