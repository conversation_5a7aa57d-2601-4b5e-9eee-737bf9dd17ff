from typing import Optional
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session

from app.db.session import get_db
from app.schemas.sys.tenant import SysTenantCreate, SysTenantUpdate, SysTenantResponse, SysTenantListResponse
from app.schemas.base import PaginationParams, ResponseModel, PaginationResponse
from app.services.sys.tenant import create_tenant, get_tenant, get_tenants, update_tenant, delete_tenant, get_active_tenants
from app.core.deps import get_sys_admin_user

router = APIRouter()


@router.post("/", response_model=SysTenantResponse, summary="创建租户")
def create_sys_tenant(
    tenant_data: SysTenantCreate,
    current_user: dict = Depends(get_sys_admin_user),
    db: Session = Depends(get_db)
):
    """
    创建租户（系统管理员可操作）
    
    - **code**: 租户代号
    - **name**: 租户名称
    - **notes**: 备注
    - **active**: 是否有效
    """
    return create_tenant(db, tenant_data)


@router.get("/{tenant_id}", response_model=SysTenantResponse, summary="获取租户详情")
def get_sys_tenant(
    tenant_id: int,
    current_user: dict = Depends(get_sys_admin_user),
    db: Session = Depends(get_db)
):
    """
    获取租户详情（系统管理员可操作）
    """
    tenant = get_tenant(db, tenant_id)
    if not tenant:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="租户不存在"
        )
    return tenant


@router.get("/", response_model=dict, summary="获取租户列表")
def get_sys_tenants(
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页数量"),
    search: Optional[str] = Query(None, description="搜索关键词"),
    active: Optional[int] = Query(None, description="状态过滤"),
    current_user: dict = Depends(get_sys_admin_user),
    db: Session = Depends(get_db)
):
    """
    获取租户列表（系统管理员可操作）
    
    - **page**: 页码
    - **page_size**: 每页数量
    - **search**: 搜索关键词（租户代号或名称）
    - **active**: 状态过滤（0：失效；1：有效）
    """
    pagination = PaginationParams(page=page, page_size=page_size)
    tenants, total = get_tenants(db, pagination, search, active)
    
    pages = (total + page_size - 1) // page_size
    
    return {
        "items": tenants,
        "pagination": PaginationResponse(
            total=total,
            page=page,
            page_size=page_size,
            pages=pages
        )
    }


@router.get("/active/list", response_model=list[SysTenantListResponse], summary="获取有效租户列表")
def get_active_sys_tenants(
    current_user: dict = Depends(get_sys_admin_user),
    db: Session = Depends(get_db)
):
    """
    获取所有有效的租户列表（系统管理员可操作）
    """
    return get_active_tenants(db)


@router.put("/{tenant_id}", response_model=SysTenantResponse, summary="更新租户")
def update_sys_tenant(
    tenant_id: int,
    tenant_data: SysTenantUpdate,
    current_user: dict = Depends(get_sys_admin_user),
    db: Session = Depends(get_db)
):
    """
    更新租户（系统管理员可操作）
    
    - **code**: 租户代号
    - **name**: 租户名称
    - **notes**: 备注
    - **active**: 是否有效
    """
    tenant = update_tenant(db, tenant_id, tenant_data)
    if not tenant:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="租户不存在"
        )
    return tenant


@router.delete("/{tenant_id}", response_model=ResponseModel, summary="删除租户")
def delete_sys_tenant(
    tenant_id: int,
    current_user: dict = Depends(get_sys_admin_user),
    db: Session = Depends(get_db)
):
    """
    删除租户（软删除，系统管理员可操作）
    """
    success = delete_tenant(db, tenant_id)
    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="租户不存在"
        )
    
    return ResponseModel(message="租户删除成功")
