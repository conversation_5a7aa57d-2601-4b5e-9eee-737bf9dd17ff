from typing import Optional, Dict, Any
from sqlalchemy.orm import Session
from fastapi import HTTPException, status
from datetime import timedelta

from app.models.models import SysUser, SysAdmin, TntAdmin
from app.core.security import create_access_token
from app.core.config import settings
from app.utils.crypt import verify_password, get_password_hash


class AuthService:
    """认证服务"""
    
    @staticmethod
    def authenticate_user(db: Session, username: str, password: str, user_type: str) -> Optional[Dict[str, Any]]:
        """
        用户认证
        
        Args:
            db: 数据库会话
            username: 用户名
            password: 密码
            user_type: 用户类型 (sys/tnt)
            
        Returns:
            用户信息字典或None
        """
        # 查找用户
        user = db.query(SysUser).filter(
            SysUser.username == username,
            SysUser.active == 1
        ).first()
        
        if not user or not verify_password(password, user.passwd):
            return None
            
        # 根据用户类型验证权限
        if user_type == "sys":
            # 系统管理员登录
            admin = db.query(SysAdmin).filter(SysAdmin.uid == user.id).first()
            if not admin:
                return None
            return {
                "user": user,
                "admin": admin,
                "user_type": "sys",
                "role": admin.role,
                "tenant_id": None
            }
        elif user_type == "tnt":
            # 租户管理员登录
            tnt_admin = db.query(TntAdmin).filter(TntAdmin.uid == user.id).first()
            if not tnt_admin:
                return None
            return {
                "user": user,
                "admin": tnt_admin,
                "user_type": "tnt",
                "role": tnt_admin.role,
                "tenant_id": tnt_admin.tenant_id
            }
        
        return None
    
    @staticmethod
    def create_token(user_info: Dict[str, Any]) -> Dict[str, Any]:
        """
        创建访问令牌
        
        Args:
            user_info: 用户信息
            
        Returns:
            令牌信息
        """
        user = user_info["user"]
        access_token_expires = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
        access_token = create_access_token(
            subject=user.id,
            token_version=user.token_version,
            expires_delta=access_token_expires
        )
        
        return {
            "access_token": access_token,
            "token_type": "bearer",
            "expires_in": settings.ACCESS_TOKEN_EXPIRE_MINUTES * 60,
            "user_info": {
                "id": user.id,
                "username": user.username,
                "name": user_info["admin"].name,
                "user_type": user_info["user_type"],
                "role": user_info["role"],
                "tenant_id": user_info["tenant_id"]
            }
        }
    
    @staticmethod
    def change_password(db: Session, user_id: int, old_password: str, new_password: str) -> bool:
        """
        修改密码
        
        Args:
            db: 数据库会话
            user_id: 用户ID
            old_password: 原密码
            new_password: 新密码
            
        Returns:
            是否成功
        """
        user = db.query(SysUser).filter(SysUser.id == user_id).first()
        if not user:
            return False
            
        if not verify_password(old_password, user.passwd):
            return False
            
        user.passwd = get_password_hash(new_password)
        user.token_version += 1  # 使所有现有token失效
        db.commit()
        return True
    
    @staticmethod
    def logout_user(db: Session, user_id: int) -> bool:
        """
        用户登出（使token失效）
        
        Args:
            db: 数据库会话
            user_id: 用户ID
            
        Returns:
            是否成功
        """
        user = db.query(SysUser).filter(SysUser.id == user_id).first()
        if not user:
            return False
            
        user.token_version += 1  # 使所有现有token失效
        db.commit()
        return True
