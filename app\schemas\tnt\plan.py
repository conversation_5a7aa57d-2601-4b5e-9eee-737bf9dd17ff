from typing import Optional, List
from pydantic import BaseModel

from app.schemas.base import BaseSchema


class TntPlanBase(BaseSchema):
    """练习计划基础Schema"""
    name: str
    pic: Optional[str] = None
    description: Optional[str] = None
    notes: Optional[str] = None
    active: Optional[int] = 1


class TntPlanCreate(TntPlanBase):
    """创建练习计划"""
    tenant_id: int


class TntPlanUpdate(BaseSchema):
    """更新练习计划"""
    name: Optional[str] = None
    pic: Optional[str] = None
    description: Optional[str] = None
    notes: Optional[str] = None
    active: Optional[int] = None


class TntPlanResponse(TntPlanBase):
    """练习计划响应"""
    id: int
    tenant_id: int


class TntPlanListResponse(BaseSchema):
    """练习计划列表响应"""
    id: int
    tenant_id: int
    name: str
    pic: Optional[str] = None
    description: Optional[str] = None
    active: int


class TntPlanExerciseBase(BaseSchema):
    """计划练习关系基础Schema"""
    depend: Optional[int] = 1  # 0：不依赖；1：依赖
    priority: Optional[int] = 1


class TntPlanExerciseCreate(TntPlanExerciseBase):
    """创建计划练习关系"""
    pid: int
    eid: int
    tenant_id: int


class TntPlanExerciseUpdate(BaseSchema):
    """更新计划练习关系"""
    depend: Optional[int] = None
    priority: Optional[int] = None


class TntPlanExerciseResponse(TntPlanExerciseBase):
    """计划练习关系响应"""
    id: int
    pid: int
    eid: int
    tenant_id: int


class TntPlanExerciseListUpdate(BaseSchema):
    """批量更新计划练习关系"""
    exercises: List[dict]  # [{"eid": 1, "depend": 1, "priority": 1}, ...]


class TntPlanDetailResponse(TntPlanResponse):
    """练习计划详情响应（包含关联练习）"""
    exercises: List[dict] = []  # 关联的练习列表
