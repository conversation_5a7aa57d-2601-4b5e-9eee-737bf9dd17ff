from typing import List, Optional
from sqlalchemy.orm import Session
from fastapi import HTTPException, status

from app.models.models import SysBot
from app.schemas.sys.bot import SysBotCreate, SysBotUpdate, SysBotResponse, SysBotListResponse
from app.schemas.base import PaginationParams


def create_bot(db: Session, bot_data: SysBotCreate) -> SysBotResponse:
    """创建机器人"""
    # 检查机器人名称是否已存在
    existing_bot = db.query(SysBot).filter(SysBot.name == bot_data.name).first()
    if existing_bot:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="机器人名称已存在"
        )
    
    # 创建机器人
    db_bot = SysBot(**bot_data.model_dump())
    
    db.add(db_bot)
    db.commit()
    db.refresh(db_bot)
    
    return SysBotResponse.model_validate(db_bot)


def get_bot(db: Session, bot_id: int) -> Optional[SysBotResponse]:
    """获取机器人"""
    bot = db.query(SysBot).filter(SysBot.id == bot_id).first()
    if not bot:
        return None
    return SysBotResponse.model_validate(bot)


def get_bots(db: Session, pagination: PaginationParams, search: Optional[str] = None, active: Optional[int] = None) -> tuple[List[SysBotListResponse], int]:
    """获取机器人列表"""
    query = db.query(SysBot)
    
    # 搜索过滤
    if search:
        query = query.filter(SysBot.name.contains(search))
    
    # 状态过滤
    if active is not None:
        query = query.filter(SysBot.active == active)
    
    # 获取总数
    total = query.count()
    
    # 分页
    offset = (pagination.page - 1) * pagination.page_size
    bots = query.offset(offset).limit(pagination.page_size).all()
    
    return [SysBotListResponse.model_validate(bot) for bot in bots], total


def update_bot(db: Session, bot_id: int, bot_data: SysBotUpdate) -> Optional[SysBotResponse]:
    """更新机器人"""
    bot = db.query(SysBot).filter(SysBot.id == bot_id).first()
    if not bot:
        return None
    
    # 检查名称是否已被其他机器人使用
    if bot_data.name and bot_data.name != bot.name:
        existing_bot = db.query(SysBot).filter(
            SysBot.name == bot_data.name,
            SysBot.id != bot_id
        ).first()
        if existing_bot:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="机器人名称已存在"
            )
    
    # 更新字段
    update_data = bot_data.model_dump(exclude_unset=True)
    for field, value in update_data.items():
        setattr(bot, field, value)
    
    db.commit()
    db.refresh(bot)
    
    return SysBotResponse.model_validate(bot)


def delete_bot(db: Session, bot_id: int) -> bool:
    """删除机器人（软删除）"""
    bot = db.query(SysBot).filter(SysBot.id == bot_id).first()
    if not bot:
        return False
    
    bot.active = 0
    db.commit()
    return True


def get_active_bots(db: Session) -> List[SysBotListResponse]:
    """获取所有有效的机器人"""
    bots = db.query(SysBot).filter(SysBot.active == 1).all()
    return [SysBotListResponse.model_validate(bot) for bot in bots]
