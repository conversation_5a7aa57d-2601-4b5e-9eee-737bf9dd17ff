from typing import Optional
from pydantic import BaseModel

from app.schemas.base import BaseSchema


class TntStudentBase(BaseSchema):
    """学员基础Schema"""
    name: str
    gender: Optional[int] = 0  # 0：未知；1：男；2：女
    notes: Optional[str] = None


class TntStudentCreate(TntStudentBase):
    """创建学员"""
    uid: int
    tenant_id: int


class TntStudentUpdate(BaseSchema):
    """更新学员"""
    name: Optional[str] = None
    gender: Optional[int] = None
    notes: Optional[str] = None


class TntStudentResponse(TntStudentBase):
    """学员响应"""
    id: int
    uid: int
    tenant_id: int
    username: Optional[str] = None  # 关联用户名


class TntStudentListResponse(BaseSchema):
    """学员列表响应"""
    id: int
    uid: int
    tenant_id: int
    name: str
    gender: int
    notes: Optional[str] = None
    username: Optional[str] = None
