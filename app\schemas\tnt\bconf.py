from typing import Optional
from pydantic import BaseModel

from app.schemas.base import BaseSchema


class TntBconfBase(BaseSchema):
    """租户机器人配置基础Schema"""
    bid: int
    notes: Optional[str] = None


class TntBconfCreate(TntBconfBase):
    """创建租户机器人配置"""
    key: str
    tenant_id: int


class TntBconfUpdate(BaseSchema):
    """更新租户机器人配置"""
    bid: Optional[int] = None
    notes: Optional[str] = None


class TntBconfResponse(TntBconfBase):
    """租户机器人配置响应"""
    key: str
    tenant_id: int
    bot_name: Optional[str] = None  # 关联机器人名称


class TntBconfListResponse(BaseSchema):
    """租户机器人配置列表响应"""
    key: str
    bid: int
    tenant_id: int
    notes: Optional[str] = None
    bot_name: Optional[str] = None
