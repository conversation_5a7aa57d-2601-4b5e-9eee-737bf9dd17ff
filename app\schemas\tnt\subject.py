from typing import Optional
from pydantic import BaseModel

from app.schemas.base import BaseSchema


class TntSubjectBase(BaseSchema):
    """主题基础Schema"""
    name: str


class TntSubjectCreate(TntSubjectBase):
    """创建主题"""
    tenant_id: int


class TntSubjectUpdate(BaseSchema):
    """更新主题"""
    name: Optional[str] = None


class TntSubjectResponse(TntSubjectBase):
    """主题响应"""
    id: int
    tenant_id: int


class TntSubjectListResponse(BaseSchema):
    """主题列表响应"""
    id: int
    tenant_id: int
    name: str
