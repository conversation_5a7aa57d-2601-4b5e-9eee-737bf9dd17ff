from typing import Optional, List
from pydantic import BaseModel

from app.schemas.base import BaseSchema


class TntAdminBase(BaseSchema):
    """租户管理员基础Schema"""
    name: str
    role: Optional[int] = 1  # 1：管理员, 2：教学运营人员


class TntAdminCreate(TntAdminBase):
    """创建租户管理员"""
    uid: int
    tenant_id: int


class TntAdminUpdate(BaseSchema):
    """更新租户管理员"""
    name: Optional[str] = None
    role: Optional[int] = None


class TntAdminResponse(TntAdminBase):
    """租户管理员响应"""
    id: int
    uid: int
    tenant_id: int
    username: Optional[str] = None  # 关联用户名
    tenant_name: Optional[str] = None  # 关联租户名


class TntAdminListResponse(BaseSchema):
    """租户管理员列表响应"""
    id: int
    uid: int
    tenant_id: int
    name: str
    role: int
    username: Optional[str] = None
    tenant_name: Optional[str] = None


class TntAdminClassUpdate(BaseSchema):
    """更新运营人员关联班级"""
    class_ids: List[int]
