from typing import Optional
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session

from app.db.session import get_db
from app.schemas.tnt.admin import TntAdminCreate, TntAdminUpdate, TntAdminResponse, TntAdminListResponse, TntAdminClassUpdate
from app.schemas.base import PaginationParams, ResponseModel, PaginationResponse
from app.services.tnt.admin_service import create_admin, get_admin, get_admins, update_admin, delete_admin, update_admin_classes, get_admin_classes
from app.core.deps import get_sys_or_tenant_admin, get_tenant_id_from_user

router = APIRouter()


@router.post("/", response_model=TntAdminResponse, summary="创建租户管理员")
def create_tenant_admin(
    admin_data: TntAdminCreate,
    current_user: dict = Depends(get_sys_or_tenant_admin),
    db: Session = Depends(get_db)
):
    """
    创建租户管理员（系统管理员或租户管理员可操作）
    
    - **uid**: 用户ID
    - **tenant_id**: 租户ID
    - **name**: 姓名
    - **role**: 角色（1：管理员, 2：教学运营人员）
    """
    # 如果是租户管理员，只能在自己的租户下创建
    if current_user.get("user_type") == "tenant_admin":
        tenant_id = get_tenant_id_from_user(current_user)
        if admin_data.tenant_id != tenant_id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="只能在自己的租户下创建管理员"
            )
    
    return create_admin(db, admin_data)


@router.get("/{admin_id}", response_model=TntAdminResponse, summary="获取租户管理员详情")
def get_tenant_admin(
    admin_id: int,
    current_user: dict = Depends(get_sys_or_tenant_admin),
    db: Session = Depends(get_db)
):
    """
    获取租户管理员详情
    """
    tenant_id = None
    if current_user.get("user_type") == "tenant_admin":
        tenant_id = get_tenant_id_from_user(current_user)
    
    admin = get_admin(db, admin_id, tenant_id)
    if not admin:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="管理员不存在"
        )
    return admin


@router.get("/", response_model=dict, summary="获取租户管理员列表")
def get_tenant_admins(
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页数量"),
    tenant_id: Optional[int] = Query(None, description="租户ID过滤"),
    role: Optional[int] = Query(None, description="角色过滤"),
    search: Optional[str] = Query(None, description="搜索关键词"),
    current_user: dict = Depends(get_sys_or_tenant_admin),
    db: Session = Depends(get_db)
):
    """
    获取租户管理员列表
    
    - **page**: 页码
    - **page_size**: 每页数量
    - **tenant_id**: 租户ID过滤（系统管理员可用）
    - **role**: 角色过滤（1：管理员, 2：教学运营人员）
    - **search**: 搜索关键词（姓名或用户名）
    """
    # 如果是租户管理员，只能查看自己租户下的管理员
    if current_user.get("user_type") == "tenant_admin":
        tenant_id = get_tenant_id_from_user(current_user)
    
    pagination = PaginationParams(page=page, page_size=page_size)
    admins, total = get_admins(db, pagination, tenant_id, role, search)
    
    pages = (total + page_size - 1) // page_size
    
    return {
        "items": admins,
        "pagination": PaginationResponse(
            total=total,
            page=page,
            page_size=page_size,
            pages=pages
        )
    }


@router.put("/{admin_id}", response_model=TntAdminResponse, summary="更新租户管理员")
def update_tenant_admin(
    admin_id: int,
    admin_data: TntAdminUpdate,
    current_user: dict = Depends(get_sys_or_tenant_admin),
    db: Session = Depends(get_db)
):
    """
    更新租户管理员
    
    - **name**: 姓名
    - **role**: 角色
    """
    tenant_id = None
    if current_user.get("user_type") == "tenant_admin":
        tenant_id = get_tenant_id_from_user(current_user)
    
    admin = update_admin(db, admin_id, admin_data, tenant_id)
    if not admin:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="管理员不存在"
        )
    return admin


@router.delete("/{admin_id}", response_model=ResponseModel, summary="删除租户管理员")
def delete_tenant_admin(
    admin_id: int,
    current_user: dict = Depends(get_sys_or_tenant_admin),
    db: Session = Depends(get_db)
):
    """
    删除租户管理员
    """
    tenant_id = None
    if current_user.get("user_type") == "tenant_admin":
        tenant_id = get_tenant_id_from_user(current_user)
    
    success = delete_admin(db, admin_id, tenant_id)
    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="管理员不存在"
        )
    
    return ResponseModel(message="管理员删除成功")


@router.put("/{admin_id}/classes", response_model=ResponseModel, summary="更新运营人员关联班级")
def update_admin_class_relations(
    admin_id: int,
    class_data: TntAdminClassUpdate,
    current_user: dict = Depends(get_sys_or_tenant_admin),
    db: Session = Depends(get_db)
):
    """
    更新运营人员关联班级
    
    - **class_ids**: 班级ID列表
    """
    tenant_id = get_tenant_id_from_user(current_user)
    if not tenant_id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="需要租户管理员权限"
        )
    
    success = update_admin_classes(db, admin_id, class_data, tenant_id)
    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="运营人员不存在或不属于该租户"
        )
    
    return ResponseModel(message="班级关联更新成功")


@router.get("/{admin_id}/classes", response_model=list[dict], summary="获取运营人员关联班级")
def get_admin_class_relations(
    admin_id: int,
    current_user: dict = Depends(get_sys_or_tenant_admin),
    db: Session = Depends(get_db)
):
    """
    获取运营人员关联的班级列表
    """
    tenant_id = get_tenant_id_from_user(current_user)
    if not tenant_id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="需要租户管理员权限"
        )
    
    return get_admin_classes(db, admin_id, tenant_id)
