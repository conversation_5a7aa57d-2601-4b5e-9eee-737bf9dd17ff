from datetime import datetime
from typing import Optional, List
from pydantic import BaseModel

from app.schemas.base import BaseSchema


class TntExerciseBase(BaseSchema):
    """练习基础Schema"""
    title: str
    type: int = 1  # 1：作业单；2：角色扮演
    intro: Optional[str] = None
    duration: Optional[int] = None
    version: Optional[str] = None
    bgtext: Optional[str] = None
    bgvideo: Optional[str] = None
    notes: Optional[str] = None
    published: Optional[int] = 0  # 0：未发布；1：已发布
    active: Optional[int] = 1


class TntExerciseCreate(TntExerciseBase):
    """创建练习"""
    tenant_id: int


class TntExerciseUpdate(BaseSchema):
    """更新练习"""
    title: Optional[str] = None
    type: Optional[int] = None
    intro: Optional[str] = None
    duration: Optional[int] = None
    version: Optional[str] = None
    bgtext: Optional[str] = None
    bgvideo: Optional[str] = None
    notes: Optional[str] = None
    published: Optional[int] = None
    active: Optional[int] = None


class TntExerciseResponse(TntExerciseBase):
    """练习响应"""
    id: int
    tenant_id: int
    ctime: datetime


class TntExerciseListResponse(BaseSchema):
    """练习列表响应"""
    id: int
    tenant_id: int
    title: str
    type: int
    intro: Optional[str] = None
    duration: Optional[int] = None
    published: int
    ctime: datetime
    active: int


class TntWorksheetBase(BaseSchema):
    """作业单基础Schema"""
    pass


class TntWorksheetCreate(TntWorksheetBase):
    """创建作业单"""
    eid: int
    tenant_id: int


class TntWorksheetResponse(TntWorksheetBase):
    """作业单响应"""
    id: int
    eid: int
    tenant_id: int


class TntUnitBase(BaseSchema):
    """单元模块基础Schema"""
    name: str
    bgtext: Optional[str] = None
    bgvideo: Optional[str] = None
    priority: Optional[int] = 1


class TntUnitCreate(TntUnitBase):
    """创建单元模块"""
    wid: int
    tenant_id: int


class TntUnitUpdate(BaseSchema):
    """更新单元模块"""
    name: Optional[str] = None
    bgtext: Optional[str] = None
    bgvideo: Optional[str] = None
    priority: Optional[int] = None


class TntUnitResponse(TntUnitBase):
    """单元模块响应"""
    id: int
    wid: int
    tenant_id: int


class TntUnitListResponse(BaseSchema):
    """单元模块列表响应"""
    id: int
    wid: int
    tenant_id: int
    name: str
    priority: int


class TntWorksheetAsmBase(BaseSchema):
    """作业单构成基础Schema"""
    priority: Optional[int] = 1


class TntWorksheetAsmCreate(TntWorksheetAsmBase):
    """创建作业单构成"""
    wid: int
    uid: int
    qid: int
    tenant_id: int


class TntWorksheetAsmUpdate(BaseSchema):
    """更新作业单构成"""
    priority: Optional[int] = None


class TntWorksheetAsmResponse(TntWorksheetAsmBase):
    """作业单构成响应"""
    id: int
    wid: int
    uid: int
    qid: int
    tenant_id: int


class TntWorksheetDetailResponse(TntWorksheetResponse):
    """作业单详情响应（包含单元模块和问题）"""
    units: List[dict] = []  # 单元模块列表，包含问题
