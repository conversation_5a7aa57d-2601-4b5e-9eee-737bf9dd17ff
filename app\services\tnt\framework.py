from typing import List, Optional
from sqlalchemy.orm import Session, joinedload
from fastapi import HTTPException, status

from app.models.models import TntFramework, TntModule
from app.schemas.tnt.framework import (
    TntFrameworkCreate, TntFrameworkUpdate, TntFrameworkResponse, TntFrameworkListResponse, TntFrameworkDetailResponse,
    TntModuleCreate, TntModuleUpdate, TntModuleResponse, TntModuleListResponse
)
from app.schemas.base import PaginationParams


def create_framework(db: Session, framework_data: TntFrameworkCreate) -> TntFrameworkResponse:
    """创建理论框架"""
    # 检查框架名称在该租户下是否已存在
    existing_framework = db.query(TntFramework).filter(
        TntFramework.name == framework_data.name,
        TntFramework.tenant_id == framework_data.tenant_id
    ).first()
    if existing_framework:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="理论框架名称在该租户下已存在"
        )
    
    # 创建框架
    db_framework = TntFramework(**framework_data.model_dump())
    
    db.add(db_framework)
    db.commit()
    db.refresh(db_framework)
    
    return TntFrameworkResponse.model_validate(db_framework)


def get_framework(db: Session, framework_id: int, tenant_id: int) -> Optional[TntFrameworkResponse]:
    """获取理论框架"""
    framework = db.query(TntFramework).filter(
        TntFramework.id == framework_id,
        TntFramework.tenant_id == tenant_id
    ).first()
    if not framework:
        return None
    return TntFrameworkResponse.model_validate(framework)


def get_framework_detail(db: Session, framework_id: int, tenant_id: int) -> Optional[TntFrameworkDetailResponse]:
    """获取理论框架详情（包含模块列表）"""
    framework = db.query(TntFramework).filter(
        TntFramework.id == framework_id,
        TntFramework.tenant_id == tenant_id
    ).first()
    if not framework:
        return None
    
    # 获取模块列表
    modules = db.query(TntModule).filter(
        TntModule.fid == framework_id,
        TntModule.tenant_id == tenant_id,
        TntModule.active == 1
    ).order_by(TntModule.priority).all()
    
    response = TntFrameworkDetailResponse.model_validate(framework)
    response.modules = [TntModuleListResponse.model_validate(module) for module in modules]
    return response


def get_frameworks(db: Session, pagination: PaginationParams, tenant_id: int, 
                   search: Optional[str] = None, active: Optional[int] = None) -> tuple[List[TntFrameworkListResponse], int]:
    """获取理论框架列表"""
    query = db.query(TntFramework).filter(TntFramework.tenant_id == tenant_id)
    
    # 搜索过滤
    if search:
        query = query.filter(TntFramework.name.contains(search))
    
    # 状态过滤
    if active is not None:
        query = query.filter(TntFramework.active == active)
    
    # 获取总数
    total = query.count()
    
    # 分页
    offset = (pagination.page - 1) * pagination.page_size
    frameworks = query.order_by(TntFramework.priority).offset(offset).limit(pagination.page_size).all()
    
    return [TntFrameworkListResponse.model_validate(framework) for framework in frameworks], total


def update_framework(db: Session, framework_id: int, tenant_id: int, framework_data: TntFrameworkUpdate) -> Optional[TntFrameworkResponse]:
    """更新理论框架"""
    framework = db.query(TntFramework).filter(
        TntFramework.id == framework_id,
        TntFramework.tenant_id == tenant_id
    ).first()
    if not framework:
        return None
    
    # 检查名称是否已被其他框架使用
    if framework_data.name and framework_data.name != framework.name:
        existing_framework = db.query(TntFramework).filter(
            TntFramework.name == framework_data.name,
            TntFramework.tenant_id == tenant_id,
            TntFramework.id != framework_id
        ).first()
        if existing_framework:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="理论框架名称在该租户下已存在"
            )
    
    # 更新字段
    update_data = framework_data.model_dump(exclude_unset=True)
    for field, value in update_data.items():
        setattr(framework, field, value)
    
    db.commit()
    db.refresh(framework)
    
    return TntFrameworkResponse.model_validate(framework)


def delete_framework(db: Session, framework_id: int, tenant_id: int) -> bool:
    """删除理论框架（软删除）"""
    framework = db.query(TntFramework).filter(
        TntFramework.id == framework_id,
        TntFramework.tenant_id == tenant_id
    ).first()
    if not framework:
        return False
    
    framework.active = 0
    db.commit()
    return True


# 理论模块相关服务

def create_module(db: Session, module_data: TntModuleCreate) -> TntModuleResponse:
    """创建理论模块"""
    # 检查框架是否存在
    framework = db.query(TntFramework).filter(
        TntFramework.id == module_data.fid,
        TntFramework.tenant_id == module_data.tenant_id,
        TntFramework.active == 1
    ).first()
    if not framework:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="理论框架不存在或已被禁用"
        )
    
    # 检查模块名称在该框架下是否已存在
    existing_module = db.query(TntModule).filter(
        TntModule.name == module_data.name,
        TntModule.fid == module_data.fid,
        TntModule.tenant_id == module_data.tenant_id
    ).first()
    if existing_module:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="理论模块名称在该框架下已存在"
        )
    
    # 创建模块
    db_module = TntModule(**module_data.model_dump())
    
    db.add(db_module)
    db.commit()
    db.refresh(db_module)
    
    return TntModuleResponse.model_validate(db_module)


def get_module(db: Session, module_id: int, tenant_id: int) -> Optional[TntModuleResponse]:
    """获取理论模块"""
    module = db.query(TntModule).filter(
        TntModule.id == module_id,
        TntModule.tenant_id == tenant_id
    ).first()
    if not module:
        return None
    return TntModuleResponse.model_validate(module)


def get_modules_by_framework(db: Session, framework_id: int, tenant_id: int) -> List[TntModuleListResponse]:
    """获取框架下的模块列表"""
    modules = db.query(TntModule).filter(
        TntModule.fid == framework_id,
        TntModule.tenant_id == tenant_id,
        TntModule.active == 1
    ).order_by(TntModule.priority).all()
    
    return [TntModuleListResponse.model_validate(module) for module in modules]


def update_module(db: Session, module_id: int, tenant_id: int, module_data: TntModuleUpdate) -> Optional[TntModuleResponse]:
    """更新理论模块"""
    module = db.query(TntModule).filter(
        TntModule.id == module_id,
        TntModule.tenant_id == tenant_id
    ).first()
    if not module:
        return None
    
    # 检查名称是否已被其他模块使用
    if module_data.name and module_data.name != module.name:
        existing_module = db.query(TntModule).filter(
            TntModule.name == module_data.name,
            TntModule.fid == module.fid,
            TntModule.tenant_id == tenant_id,
            TntModule.id != module_id
        ).first()
        if existing_module:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="理论模块名称在该框架下已存在"
            )
    
    # 更新字段
    update_data = module_data.model_dump(exclude_unset=True)
    for field, value in update_data.items():
        setattr(module, field, value)
    
    db.commit()
    db.refresh(module)
    
    return TntModuleResponse.model_validate(module)


def delete_module(db: Session, module_id: int, tenant_id: int) -> bool:
    """删除理论模块（软删除）"""
    module = db.query(TntModule).filter(
        TntModule.id == module_id,
        TntModule.tenant_id == tenant_id
    ).first()
    if not module:
        return False
    
    module.active = 0
    db.commit()
    return True
