from typing import List, Optional
from sqlalchemy.orm import Session
from fastapi import HTTPException, status

from app.models.models import SysTenant
from app.schemas.sys.tenant import SysTenantCreate, SysTenantUpdate, SysTenantResponse, SysTenantListResponse
from app.schemas.base import PaginationParams


def create_tenant(db: Session, tenant_data: SysTenantCreate) -> SysTenantResponse:
    """创建租户"""
    # 检查租户代号是否已存在
    existing_tenant = db.query(SysTenant).filter(SysTenant.code == tenant_data.code).first()
    if existing_tenant:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="租户代号已存在"
        )
    
    # 创建租户
    db_tenant = SysTenant(**tenant_data.model_dump())
    
    db.add(db_tenant)
    db.commit()
    db.refresh(db_tenant)
    
    return SysTenantResponse.model_validate(db_tenant)


def get_tenant(db: Session, tenant_id: int) -> Optional[SysTenantResponse]:
    """获取租户"""
    tenant = db.query(SysTenant).filter(SysTenant.id == tenant_id).first()
    if not tenant:
        return None
    return SysTenantResponse.model_validate(tenant)


def get_tenants(db: Session, pagination: PaginationParams, search: Optional[str] = None, active: Optional[int] = None) -> tuple[List[SysTenantListResponse], int]:
    """获取租户列表"""
    query = db.query(SysTenant)
    
    # 搜索过滤
    if search:
        query = query.filter(
            (SysTenant.code.contains(search)) | 
            (SysTenant.name.contains(search))
        )
    
    # 状态过滤
    if active is not None:
        query = query.filter(SysTenant.active == active)
    
    # 获取总数
    total = query.count()
    
    # 分页
    offset = (pagination.page - 1) * pagination.page_size
    tenants = query.order_by(SysTenant.ctime.desc()).offset(offset).limit(pagination.page_size).all()
    
    return [SysTenantListResponse.model_validate(tenant) for tenant in tenants], total


def update_tenant(db: Session, tenant_id: int, tenant_data: SysTenantUpdate) -> Optional[SysTenantResponse]:
    """更新租户"""
    tenant = db.query(SysTenant).filter(SysTenant.id == tenant_id).first()
    if not tenant:
        return None
    
    # 检查代号是否已被其他租户使用
    if tenant_data.code and tenant_data.code != tenant.code:
        existing_tenant = db.query(SysTenant).filter(
            SysTenant.code == tenant_data.code,
            SysTenant.id != tenant_id
        ).first()
        if existing_tenant:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="租户代号已存在"
            )
    
    # 更新字段
    update_data = tenant_data.model_dump(exclude_unset=True)
    for field, value in update_data.items():
        setattr(tenant, field, value)
    
    db.commit()
    db.refresh(tenant)
    
    return SysTenantResponse.model_validate(tenant)


def delete_tenant(db: Session, tenant_id: int) -> bool:
    """删除租户（软删除）"""
    tenant = db.query(SysTenant).filter(SysTenant.id == tenant_id).first()
    if not tenant:
        return False
    
    tenant.active = 0
    db.commit()
    return True


def get_active_tenants(db: Session) -> List[SysTenantListResponse]:
    """获取所有有效的租户"""
    tenants = db.query(SysTenant).filter(SysTenant.active == 1).all()
    return [SysTenantListResponse.model_validate(tenant) for tenant in tenants]


def get_tenant_by_code(db: Session, code: str) -> Optional[SysTenant]:
    """根据代号获取租户"""
    return db.query(SysTenant).filter(SysTenant.code == code, SysTenant.active == 1).first()
