from datetime import datetime
from typing import Optional, List
from pydantic import BaseModel

from app.schemas.base import BaseSchema


class TntClassBase(BaseSchema):
    """班级基础Schema"""
    name: str
    pic: Optional[str] = None
    description: Optional[str] = None
    notes: Optional[str] = None
    btime: datetime
    etime: datetime
    active: Optional[int] = 1


class TntClassCreate(TntClassBase):
    """创建班级"""
    tenant_id: int


class TntClassUpdate(BaseSchema):
    """更新班级"""
    name: Optional[str] = None
    pic: Optional[str] = None
    description: Optional[str] = None
    notes: Optional[str] = None
    btime: Optional[datetime] = None
    etime: Optional[datetime] = None
    active: Optional[int] = None


class TntClassResponse(TntClassBase):
    """班级响应"""
    id: int
    tenant_id: int
    ctime: datetime


class TntClassListResponse(BaseSchema):
    """班级列表响应"""
    id: int
    tenant_id: int
    name: str
    pic: Optional[str] = None
    description: Optional[str] = None
    btime: datetime
    etime: datetime
    ctime: datetime
    active: int


class TntClassStudentUpdate(BaseSchema):
    """更新班级学员关系"""
    student_ids: List[int]


class TntClassExerciseBase(BaseSchema):
    """班级练习关系基础Schema"""
    depend: Optional[int] = 1  # 0：不依赖；1：依赖
    priority: Optional[int] = 1
    active: Optional[int] = 1


class TntClassExerciseCreate(TntClassExerciseBase):
    """创建班级练习关系"""
    cid: int
    eid: int
    tid: int
    tenant_id: int


class TntClassExerciseUpdate(BaseSchema):
    """更新班级练习关系"""
    tid: Optional[int] = None
    depend: Optional[int] = None
    priority: Optional[int] = None
    active: Optional[int] = None


class TntClassExerciseResponse(TntClassExerciseBase):
    """班级练习关系响应"""
    id: int
    cid: int
    eid: int
    tid: int
    tenant_id: int


class TntClassExerciseListUpdate(BaseSchema):
    """批量更新班级练习关系"""
    exercises: List[dict]  # [{"eid": 1, "tid": 1, "depend": 1, "priority": 1}, ...]


class TntClassExerciseCopyFromPlan(BaseSchema):
    """从练习计划复制练习到班级"""
    plan_id: int
    teacher_id: int


class TntClassDetailResponse(TntClassResponse):
    """班级详情响应（包含学员、练习、运营人员）"""
    students: List[dict] = []  # 关联学员列表
    exercises: List[dict] = []  # 关联练习列表
    admins: List[dict] = []  # 关联运营人员列表
