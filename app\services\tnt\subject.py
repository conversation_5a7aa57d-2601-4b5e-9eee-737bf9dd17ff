from typing import List, Optional
from sqlalchemy.orm import Session
from fastapi import HTTPException, status

from app.models.models import TntSubject
from app.schemas.tnt.subject import TntSubjectCreate, TntSubjectUpdate, TntSubjectResponse, TntSubjectListResponse
from app.schemas.base import PaginationParams


def create_subject(db: Session, subject_data: TntSubjectCreate) -> TntSubjectResponse:
    """创建主题"""
    # 检查主题名称在该租户下是否已存在
    existing_subject = db.query(TntSubject).filter(
        TntSubject.name == subject_data.name,
        TntSubject.tenant_id == subject_data.tenant_id
    ).first()
    if existing_subject:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="主题名称在该租户下已存在"
        )
    
    # 创建主题
    db_subject = TntSubject(**subject_data.model_dump())
    
    db.add(db_subject)
    db.commit()
    db.refresh(db_subject)
    
    return TntSubjectResponse.model_validate(db_subject)


def get_subject(db: Session, subject_id: int, tenant_id: int) -> Optional[TntSubjectResponse]:
    """获取主题"""
    subject = db.query(TntSubject).filter(
        TntSubject.id == subject_id,
        TntSubject.tenant_id == tenant_id
    ).first()
    if not subject:
        return None
    return TntSubjectResponse.model_validate(subject)


def get_subjects(db: Session, pagination: PaginationParams, tenant_id: int, 
                 search: Optional[str] = None) -> tuple[List[TntSubjectListResponse], int]:
    """获取主题列表"""
    query = db.query(TntSubject).filter(TntSubject.tenant_id == tenant_id)
    
    # 搜索过滤
    if search:
        query = query.filter(TntSubject.name.contains(search))
    
    # 获取总数
    total = query.count()
    
    # 分页
    offset = (pagination.page - 1) * pagination.page_size
    subjects = query.offset(offset).limit(pagination.page_size).all()
    
    return [TntSubjectListResponse.model_validate(subject) for subject in subjects], total


def update_subject(db: Session, subject_id: int, tenant_id: int, subject_data: TntSubjectUpdate) -> Optional[TntSubjectResponse]:
    """更新主题"""
    subject = db.query(TntSubject).filter(
        TntSubject.id == subject_id,
        TntSubject.tenant_id == tenant_id
    ).first()
    if not subject:
        return None
    
    # 检查名称是否已被其他主题使用
    if subject_data.name and subject_data.name != subject.name:
        existing_subject = db.query(TntSubject).filter(
            TntSubject.name == subject_data.name,
            TntSubject.tenant_id == tenant_id,
            TntSubject.id != subject_id
        ).first()
        if existing_subject:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="主题名称在该租户下已存在"
            )
    
    # 更新字段
    update_data = subject_data.model_dump(exclude_unset=True)
    for field, value in update_data.items():
        setattr(subject, field, value)
    
    db.commit()
    db.refresh(subject)
    
    return TntSubjectResponse.model_validate(subject)


def delete_subject(db: Session, subject_id: int, tenant_id: int) -> bool:
    """删除主题"""
    subject = db.query(TntSubject).filter(
        TntSubject.id == subject_id,
        TntSubject.tenant_id == tenant_id
    ).first()
    if not subject:
        return False
    
    db.delete(subject)
    db.commit()
    return True


def get_all_subjects(db: Session, tenant_id: int) -> List[TntSubjectListResponse]:
    """获取租户下所有主题"""
    subjects = db.query(TntSubject).filter(TntSubject.tenant_id == tenant_id).all()
    return [TntSubjectListResponse.model_validate(subject) for subject in subjects]
