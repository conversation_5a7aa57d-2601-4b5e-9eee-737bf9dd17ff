from datetime import datetime
from typing import Optional
from pydantic import BaseModel

from app.schemas.base import BaseSchema


class TntTeacherBase(BaseSchema):
    """老师基础Schema"""
    name: str
    gender: Optional[int] = 0  # 0：未知；1：男；2：女
    avatar: str
    intro: Optional[str] = None
    notes: Optional[str] = None


class TntTeacherCreate(TntTeacherBase):
    """创建老师"""
    tenant_id: int


class TntTeacherUpdate(BaseSchema):
    """更新老师"""
    name: Optional[str] = None
    gender: Optional[int] = None
    avatar: Optional[str] = None
    intro: Optional[str] = None
    notes: Optional[str] = None


class TntTeacherResponse(TntTeacherBase):
    """老师响应"""
    id: int
    tenant_id: int
    ctime: datetime


class TntTeacherListResponse(BaseSchema):
    """老师列表响应"""
    id: int
    tenant_id: int
    name: str
    gender: int
    avatar: str
    intro: Optional[str] = None
    ctime: datetime
