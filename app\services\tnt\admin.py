from typing import List, Optional
from sqlalchemy.orm import Session, joinedload
from fastapi import HTTPException, status

from app.models.models import TntAdmin, SysUser, SysTenant, TntAdminClass, TntClass
from app.schemas.tnt.admin import TntAdminCreate, TntAdminUpdate, TntAdminResponse, TntAdminListResponse, TntAdminClassUpdate
from app.schemas.base import PaginationParams


def create_admin(db: Session, admin_data: TntAdminCreate) -> TntAdminResponse:
    """创建租户管理员"""
    # 检查用户是否存在且有效
    user = db.query(SysUser).filter(SysUser.id == admin_data.uid, SysUser.active == 1).first()
    if not user:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="用户不存在或已被禁用"
        )
    
    # 检查租户是否存在且有效
    tenant = db.query(SysTenant).filter(SysTenant.id == admin_data.tenant_id, SysTenant.active == 1).first()
    if not tenant:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="租户不存在或已被禁用"
        )
    
    # 检查用户在该租户下是否已经是管理员
    existing_admin = db.query(TntAdmin).filter(
        TntAdmin.uid == admin_data.uid,
        TntAdmin.tenant_id == admin_data.tenant_id
    ).first()
    if existing_admin:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="该用户在此租户下已经是管理员"
        )
    
    # 创建管理员
    db_admin = TntAdmin(**admin_data.model_dump())
    
    db.add(db_admin)
    db.commit()
    db.refresh(db_admin)
    
    # 返回包含用户名和租户名的响应
    response = TntAdminResponse.model_validate(db_admin)
    response.username = user.username
    response.tenant_name = tenant.name
    return response


def get_admin(db: Session, admin_id: int, tenant_id: Optional[int] = None) -> Optional[TntAdminResponse]:
    """获取租户管理员"""
    query = db.query(TntAdmin).options(
        joinedload(TntAdmin.user),
        joinedload(TntAdmin.tenant)
    ).filter(TntAdmin.id == admin_id)
    
    if tenant_id:
        query = query.filter(TntAdmin.tenant_id == tenant_id)
    
    admin = query.first()
    if not admin:
        return None
    
    response = TntAdminResponse.model_validate(admin)
    response.username = admin.user.username if admin.user else None
    response.tenant_name = admin.tenant.name if admin.tenant else None
    return response


def get_admins(db: Session, pagination: PaginationParams, tenant_id: Optional[int] = None, 
               role: Optional[int] = None, search: Optional[str] = None) -> tuple[List[TntAdminListResponse], int]:
    """获取租户管理员列表"""
    query = db.query(TntAdmin).options(
        joinedload(TntAdmin.user),
        joinedload(TntAdmin.tenant)
    )
    
    # 租户过滤
    if tenant_id:
        query = query.filter(TntAdmin.tenant_id == tenant_id)
    
    # 角色过滤
    if role:
        query = query.filter(TntAdmin.role == role)
    
    # 搜索过滤
    if search:
        query = query.join(SysUser).filter(
            (TntAdmin.name.contains(search)) | 
            (SysUser.username.contains(search))
        )
    
    # 获取总数
    total = query.count()
    
    # 分页
    offset = (pagination.page - 1) * pagination.page_size
    admins = query.offset(offset).limit(pagination.page_size).all()
    
    result = []
    for admin in admins:
        response = TntAdminListResponse.model_validate(admin)
        response.username = admin.user.username if admin.user else None
        response.tenant_name = admin.tenant.name if admin.tenant else None
        result.append(response)
    
    return result, total


def update_admin(db: Session, admin_id: int, admin_data: TntAdminUpdate, tenant_id: Optional[int] = None) -> Optional[TntAdminResponse]:
    """更新租户管理员"""
    query = db.query(TntAdmin).options(
        joinedload(TntAdmin.user),
        joinedload(TntAdmin.tenant)
    ).filter(TntAdmin.id == admin_id)
    
    if tenant_id:
        query = query.filter(TntAdmin.tenant_id == tenant_id)
    
    admin = query.first()
    if not admin:
        return None
    
    # 更新字段
    update_data = admin_data.model_dump(exclude_unset=True)
    for field, value in update_data.items():
        setattr(admin, field, value)
    
    db.commit()
    db.refresh(admin)
    
    response = TntAdminResponse.model_validate(admin)
    response.username = admin.user.username if admin.user else None
    response.tenant_name = admin.tenant.name if admin.tenant else None
    return response


def delete_admin(db: Session, admin_id: int, tenant_id: Optional[int] = None) -> bool:
    """删除租户管理员"""
    query = db.query(TntAdmin).filter(TntAdmin.id == admin_id)
    
    if tenant_id:
        query = query.filter(TntAdmin.tenant_id == tenant_id)
    
    admin = query.first()
    if not admin:
        return False
    
    # 删除关联的班级关系
    db.query(TntAdminClass).filter(TntAdminClass.aid == admin_id).delete()
    
    # 删除管理员
    db.delete(admin)
    db.commit()
    return True


def update_admin_classes(db: Session, admin_id: int, class_data: TntAdminClassUpdate, tenant_id: int) -> bool:
    """更新运营人员关联班级"""
    # 检查管理员是否存在且为运营人员
    admin = db.query(TntAdmin).filter(
        TntAdmin.id == admin_id,
        TntAdmin.tenant_id == tenant_id,
        TntAdmin.role == 2  # 教学运营人员
    ).first()
    if not admin:
        return False
    
    # 删除现有关联
    db.query(TntAdminClass).filter(TntAdminClass.aid == admin_id).delete()
    
    # 创建新关联
    for class_id in class_data.class_ids:
        # 检查班级是否存在且属于该租户
        class_exists = db.query(TntClass).filter(
            TntClass.id == class_id,
            TntClass.tenant_id == tenant_id,
            TntClass.active == 1
        ).first()
        if class_exists:
            admin_class = TntAdminClass(
                tenant_id=tenant_id,
                aid=admin_id,
                cid=class_id
            )
            db.add(admin_class)
    
    db.commit()
    return True


def get_admin_classes(db: Session, admin_id: int, tenant_id: int) -> List[dict]:
    """获取运营人员关联的班级"""
    admin_classes = db.query(TntAdminClass).options(
        joinedload(TntAdminClass.class_)
    ).filter(
        TntAdminClass.aid == admin_id,
        TntAdminClass.tenant_id == tenant_id
    ).all()
    
    result = []
    for admin_class in admin_classes:
        if admin_class.class_:
            result.append({
                "id": admin_class.class_.id,
                "name": admin_class.class_.name,
                "description": admin_class.class_.description
            })
    
    return result
